# Project Structure

## Directory Organization

The Ultimate Electrical Designer follows a monorepo architecture with clear separation of concerns across multiple service layers and comprehensive documentation structure.

```
ultimate-electrical-designer/
├── README.md                        # Project overview and quick start guide
├── CLAUDE.md                        # AI development assistant guidance
├── product.md                       # Product definition and vision
├── structure.md                     # Project organization (this file)
├── tech.md                          # Technology stack documentation
├── rules.md                         # Development standards and rules
├── requirements.md                  # Functional requirements specification
├── design.md                        # Technical architecture specification
├── tasks.md                         # Implementation task breakdown
├── API.md                           # API documentation and specifications
├── CALCULATIONS.md                  # Electrical calculations documentation
├── DATABASE.md                      # Database schema and operations
├── GUIDE.md                         # User and developer guides
├── MONITORING.md                    # System monitoring and observability
├── PLANNING.md                      # Project planning and roadmap
├── PRD.md                           # Product requirements document
├── STANDARDS.md                     # Engineering standards and compliance
├── TASKS.md                         # Task management and tracking
├── Makefile                         # Development orchestration commands
├── docker-compose.yml               # Multi-service development environment
├── .pre-commit-config.yaml          # Pre-commit hooks configuration
├── .gitignore                       # Version control exclusions
├── .env.example                     # Environment configuration template
├── dev_prompt-instructions.md       # Development prompt instructions
├── test_app.db                      # Test database file
├── folder-structure.json            # Complete project structure analysis
│
├── .augment/                        # Augment AI assistant configuration
│   └── rules/                       # Augment-specific rules and commands
│       ├── Augment.md              # Augment AI configuration
│       ├── Commands.md             # Augment commands
│       └── rules.md                # Augment development rules
├── .claude/                         # Claude AI assistant configuration
├── .kilocode/                       # Kilocode AI assistant configuration
│   ├── rules/                       # Kilocode rules and memory
│   │   ├── memory-bank/            # Memory bank directory
│   │   ├── development-standards.md # Development standards
│   │   ├── MemoryBank.md           # Memory bank documentation
│   │   └── rules.md                # Kilocode development rules
│   └── workflows/                   # Development workflows
│       ├── analyze-codebase.md     # Codebase analysis workflow
│       ├── new-client-element.md   # New client element workflow
│       ├── new-client-module.md    # New client module workflow
│       ├── new-feature.md          # New feature workflow
│       ├── new-server-element.md   # New server element workflow
│       ├── new-server-module.md    # New server module workflow
│       ├── refactor-code.md        # Code refactoring workflow
│       ├── tests-fix-all.md        # Test fixing workflow
│       ├── tests-fix-module.md     # Module test fixing workflow
│       ├── tests-module-coverage.md # Test coverage workflow
│       ├── update-client-element.md # Client element update workflow
│       ├── update-client-module.md # Client module update workflow
│       ├── update-documentation.md # Documentation update workflow
│       ├── update-feature.md       # Feature update workflow
│       ├── update-README.md        # README update workflow
│       ├── update-server-element.md # Server element update workflow
│       └── update-server-module.md # Server module update workflow
│
├── server/                          # Python FastAPI Backend Service
│   ├── src/                        # Source code directory
│   │   ├── main.py                 # Application entry point and CLI
│   │   ├── app.py                  # FastAPI application instance
│   │   ├── config/                 # Application configuration
│   │   │   ├── __init__.py
│   │   │   └── settings.py         # Environment and database settings
│   │   ├── api/                    # API layer (Layer 1)
│   │   │   └── v1/                 # API version 1
│   │   │       ├── __init__.py
│   │   │       ├── router.py       # Main API router
│   │   │       ├── auth_routes.py  # Authentication endpoints
│   │   │       ├── user_routes.py  # User management endpoints
│   │   │       ├── health_routes.py # Health check endpoints
│   │   │       ├── component_routes.py # Component management endpoints
│   │   │       ├── component_category_routes.py # Component category endpoints
│   │   │       └── component_type_routes.py # Component type endpoints
│   │   ├── middleware/              # Custom middleware
│   │   │   ├── __init__.py
│   │   │   ├── security_middleware.py # Security headers and validation
│   │   │   ├── logging_middleware.py # Request/response logging
│   │   │   ├── rate_limiting_middleware.py # Rate limiting
│   │   │   ├── caching_middleware.py # Response caching
│   │   │   └── context_middleware.py # Request context management
│   │   ├── core/                   # Core business logic (Layers 2-5)
│   │   │   ├── __init__.py
│   │   │   ├── schemas/            # Pydantic validation schemas (Layer 2)
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth_schemas.py # Authentication schemas
│   │   │   │   └── general/        # General domain schemas
│   │   │   │       ├── __init__.py
│   │   │   │       ├── user_schemas.py # User entity schemas
│   │   │   │       ├── component_schemas.py # Component entity schemas
│   │   │   │       ├── component_category_schemas.py # Component category schemas
│   │   │   │       └── component_type_schemas.py # Component type schemas
│   │   │   ├── services/           # Business logic layer (Layer 3)
│   │   │   │   ├── __init__.py
│   │   │   │   ├── dependencies.py # Service dependency injection
│   │   │   │   └── general/        # General domain services
│   │   │   │       ├── __init__.py
│   │   │   │       ├── user_service.py # User business logic
│   │   │   │       ├── component_service.py # Component business logic
│   │   │   │       ├── component_category_service.py # Component category logic
│   │   │   │       ├── component_type_service.py # Component type logic
│   │   │   │       └── health_service.py # Health check business logic
│   │   │   ├── repositories/       # Data access layer (Layer 4)
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base_repository.py # Generic CRUD repository
│   │   │   │   ├── repository_dependencies.py # Repository injection
│   │   │   │   └── general/        # General domain repositories
│   │   │   │       ├── __init__.py
│   │   │   │       ├── user_repository.py # User data access
│   │   │   │       ├── component_repository.py # Component data access
│   │   │   │       ├── component_category_repository.py # Component category data access
│   │   │   │       └── component_type_repository.py # Component type data access
│   │   │   ├── models/             # Database models (Layer 5)
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base_model.py   # Base model with common fields
│   │   │   │   └── general/        # General domain models
│   │   │   │       ├── __init__.py
│   │   │   │       ├── user.py     # User entity model
│   │   │   │       ├── component.py # Component entity model
│   │   │   │       ├── component_category.py # Component category model
│   │   │   │       └── component_type.py # Component type model
│   │   │   ├── security/           # Security validation and utilities
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth_handler.py # JWT token handling
│   │   │   │   ├── password_handler.py # Password hashing and validation
│   │   │   │   └── permissions.py  # Role-based permissions
│   │   │   ├── errors/             # Unified error handling
│   │   │   │   ├── __init__.py
│   │   │   │   ├── exceptions.py   # Custom exception classes
│   │   │   │   └── handlers.py     # Error handling decorators
│   │   │   └── utils/              # Utility functions and helpers
│   │   │       ├── __init__.py
│   │   │       ├── crud_endpoint_factory.py # CRUD endpoint generation
│   │   │       ├── pagination_utils.py # Pagination utilities
│   │   │       ├── advanced_cache_manager.py # Advanced caching
│   │   │       ├── query_optimizer.py # Database query optimization
│   │   │       └── search_query_builder.py # Search query construction
│   │   └── alembic/                # Database migrations
│   │       ├── versions/           # Migration files
│   │       │   ├── 48e289970994_add_component_table.py # Component table migration
│   │       │   ├── 4cf07113de3c_add_component_table_complete.py # Complete component table
│   │       │   └── add_component_category_and_type_tables.py # Component category and type tables
│   │       ├── alembic.ini        # Alembic configuration
│   │       ├── env.py             # Migration environment
│   │       └── script.py.mako     # Migration template
│   ├── tests/                      # Comprehensive test suite
│   │   ├── __init__.py
│   │   ├── conftest.py            # Test configuration and fixtures
│   │   ├── api/                   # API endpoint tests
│   │   │   ├── __init__.py
│   │   │   ├── conftest.py        # API test fixtures
│   │   │   └── v1/                # API v1 tests
│   │   │       ├── __init__.py
│   │   │       ├── test_auth_routes.py # Authentication endpoint tests
│   │   │       ├── test_user_routes.py # User endpoint tests
│   │   │       ├── test_health_routes.py # Health endpoint tests
│   │   │       ├── test_component_routes.py # Component endpoint tests
│   │   │       ├── test_component_category_routes.py # Component category tests
│   │   │       └── test_component_type_routes.py # Component type tests
│   │   ├── core/                  # Core business logic tests
│   │   │   ├── __init__.py
│   │   │   ├── test_models.py     # Model validation tests
│   │   │   ├── test_services.py   # Service layer tests
│   │   │   ├── test_repositories.py # Repository layer tests
│   │   │   └── test_schemas.py    # Schema validation tests
│   │   ├── integration/           # Integration tests
│   │   │   ├── __init__.py
│   │   │   ├── test_auth_integration.py # Authentication flow tests
│   │   │   ├── test_user_integration.py # User management flow tests
│   │   │   └── test_component_integration.py # Component management flow tests
│   │   ├── middleware/            # Middleware tests
│   │   │   ├── __init__.py
│   │   │   ├── conftest.py        # Middleware test fixtures
│   │   │   ├── test_security_middleware.py # Security middleware tests
│   │   │   ├── test_logging_middleware.py # Logging middleware tests
│   │   │   ├── test_rate_limiting_middleware.py # Rate limiting tests
│   │   │   ├── test_caching_middleware.py # Caching middleware tests
│   │   │   ├── test_context_middleware.py # Context middleware tests
│   │   │   └── test_middleware_integration.py # Middleware integration tests
│   │   ├── security/              # Security tests
│   │   │   ├── __init__.py
│   │   │   └── test_input_validators.py # Input validation tests
│   │   ├── performance/           # Performance tests
│   │   │   ├── __init__.py
│   │   │   ├── test_load_testing.py # Load testing
│   │   │   └── test_benchmarks.py # Performance benchmarks
│   │   └── calculations/          # Electrical calculation tests
│   │       ├── __init__.py
│   │       ├── test_heat_tracing.py # Heat tracing calculation tests
│   │       └── test_cable_sizing.py # Cable sizing calculation tests
│   ├── data/                      # Database files (SQLite development)
│   │   ├── app_dev.db            # Development database
│   │   ├── app_dev.db-shm        # SQLite shared memory
│   │   └── app_dev.db-wal        # SQLite write-ahead log
│   ├── node_modules/             # Node.js dependencies (temporary)
│   ├── test_app.db               # Test database file
│   ├── pyproject.toml            # Python dependencies and project metadata
│   ├── poetry.lock               # Dependency lock file
│   ├── mypy.ini                  # MyPy type checking configuration
│   ├── .env                      # Environment variables (not in VCS)
│   └── Dockerfile                # Container configuration
│
├── client/                        # Next.js Frontend Application
│   ├── src/                      # Source code directory
│   │   ├── app/                  # Next.js App Router
│   │   │   ├── layout.tsx        # Root layout component
│   │   │   ├── page.tsx          # Landing page
│   │   │   ├── globals.css       # Global styles
│   │   │   ├── (auth)/           # Authentication route group
│   │   │   │   └── login/
│   │   │   │       └── page.tsx  # Login page
│   │   │   ├── dashboard/        # Dashboard route
│   │   │   │   └── page.tsx      # Dashboard page
│   │   │   ├── profile/          # User profile route
│   │   │   │   └── page.tsx      # Profile page
│   │   │   ├── components/       # Component management routes
│   │   │   │   ├── page.tsx      # Component catalog page
│   │   │   │   ├── [id]/         # Component details route
│   │   │   │   │   └── page.tsx  # Component details page
│   │   │   │   ├── create/       # Component creation route
│   │   │   │   │   └── page.tsx  # Component creation page
│   │   │   │   └── edit/         # Component editing route
│   │   │   │       └── [id]/
│   │   │   │           └── page.tsx # Component editing page
│   │   │   └── README.md         # App Router documentation
│   │   │   └── components/       # Component management app pages
│   │   │       ├── page.tsx      # Component management main page
│   │   │       └── [id]/         # Dynamic component routes
│   │   │           └── page.tsx  # Individual component page
│   │   ├── components/           # Reusable UI components
│   │   │   ├── ui/               # shadcn/ui components
│   │   │   │   ├── button.tsx    # Button component
│   │   │   │   ├── input.tsx     # Input component
│   │   │   │   ├── card.tsx      # Card component
│   │   │   │   ├── dialog.tsx    # Dialog component
│   │   │   │   ├── table.tsx     # Table component
│   │   │   │   ├── select.tsx    # Select component
│   │   │   │   ├── checkbox.tsx  # Checkbox component
│   │   │   │   ├── badge.tsx     # Badge component
│   │   │   │   ├── avatar.tsx    # Avatar component
│   │   │   │   ├── toast.tsx     # Toast notification component
│   │   │   │   ├── toaster.tsx   # Toast container component
│   │   │   │   ├── pagination.tsx # Pagination component
│   │   │   │   ├── progress.tsx  # Progress indicator component
│   │   │   │   ├── tabs.tsx      # Tabs component
│   │   │   │   ├── accordion.tsx # Accordion component
│   │   │   │   ├── alert-dialog.tsx # Alert dialog component
│   │   │   │   ├── breadcrumb.tsx # Breadcrumb component
│   │   │   │   ├── calendar.tsx  # Calendar component
│   │   │   │   ├── command.tsx   # Command palette component
│   │   │   │   ├── dropdown-menu.tsx # Dropdown menu component
│   │   │   │   ├── hover-card.tsx # Hover card component
│   │   │   │   ├── label.tsx     # Label component
│   │   │   │   ├── navigation-menu.tsx # Navigation menu component
│   │   │   │   ├── popover.tsx   # Popover component
│   │   │   │   ├── radio-group.tsx # Radio group component
│   │   │   │   ├── scroll-area.tsx # Scroll area component
│   │   │   │   ├── slider.tsx    # Slider component
│   │   │   │   ├── switch.tsx    # Switch component
│   │   │   │   ├── textarea.tsx  # Textarea component
│   │   │   │   ├── toggle.tsx    # Toggle component
│   │   │   │   ├── tooltip.tsx   # Tooltip component
│   │   │   │   ├── tree.tsx      # Tree component
│   │   │   │   ├── multiselect.tsx # Multi-select component
│   │   │   │   ├── select-native.tsx # Native select component
│   │   │   │   ├── checkbox-tree.tsx # Checkbox tree component
│   │   │   │   ├── cropper.tsx   # Image cropper component
│   │   │   │   ├── datefield-rac.tsx # Date field component
│   │   │   │   ├── calendar-rac.tsx # Calendar component
│   │   │   │   ├── stepper.tsx   # Stepper component
│   │   │   │   ├── timeline.tsx  # Timeline component
│   │   │   │   ├── resizable.tsx # Resizable component
│   │   │   │   ├── toggle-group.tsx # Toggle group component
│   │   │   │   ├── collapsible.tsx # Collapsible component
│   │   │   │   └── sonner.tsx    # Sonner toast component
│   │   │   ├── common/           # Shared application components
│   │   │   │   ├── Header.tsx    # Application header
│   │   │   │   ├── Footer.tsx    # Application footer
│   │   │   │   └── LoadingSpinner.tsx # Loading indicator
│   │   │   ├── auth/             # Authentication components
│   │   │   │   ├── LoginForm.tsx # Login form component
│   │   │   │   ├── RouteGuard.tsx # Route protection component
│   │   │   │   └── UserProfile.tsx # User profile component
│   │   │   ├── admin/            # Admin dashboard components
│   │   │   │   ├── AdminDashboard.tsx # Admin dashboard
│   │   │   │   └── UserManagement.tsx # User management interface
│   │   │   ├── layout/           # Layout components
│   │   │   │   └── DashboardLayout.tsx # Dashboard layout wrapper
│   │   │   ├── navigation/       # Navigation components
│   │   │   │   ├── Sidebar.tsx   # Application sidebar
│   │   │   │   └── Breadcrumbs.tsx # Breadcrumb navigation
│   │   │   └── data-display/     # Data display components
│   │   │       ├── DataTable.tsx # Generic data table
│   │   │       ├── StatCard.tsx  # Statistics card component
│   │   │       └── Chart.tsx     # Chart component
│   │   ├── modules/              # Feature modules (Domain-Driven Design)
│   │   │   └── components/       # Component management module
│   │   │       ├── index.ts      # Module exports
│   │   │       ├── types.ts      # Module type definitions
│   │   │       ├── utils.ts      # Module utility functions
│   │   │       ├── api/          # API client for components
│   │   │       │   ├── index.ts  # API exports
│   │   │       │   ├── componentApi.ts # Component API client
│   │   │       │   ├── componentQueries.ts # React Query hooks
│   │   │       │   └── componentMutations.ts # React Query mutations
│   │   │       ├── components/   # Component management UI
│   │   │       │   ├── index.ts  # Component exports
│   │   │       │   ├── ComponentCard.tsx # Component card display
│   │   │       │   ├── ComponentList.tsx # Component listing
│   │   │       │   ├── ComponentSearch.tsx # Component search interface
│   │   │       │   ├── ComponentFilters.tsx # Component filtering
│   │   │       │   ├── ComponentForm.tsx # Component creation/editing form
│   │   │       │   ├── ComponentDetails.tsx # Component details view
│   │   │       │   ├── ComponentStats.tsx # Component statistics
│   │   │       │   └── BulkOperations.tsx # Bulk operations interface
│   │   │       ├── hooks/        # Component module hooks
│   │   │       │   ├── index.ts  # Hook exports
│   │   │       │   ├── useComponentStore.ts # Component state management
│   │   │       │   └── useComponentForm.ts # Component form logic
│   │   │       └── __tests__/    # Component module tests
│   │   │           ├── components/ # Component tests
│   │   │           ├── hooks/    # Hook tests
│   │   │           ├── api/      # API tests
│   │   │           └── utils/    # Utility tests
│   │   ├── hooks/                # General-purpose React hooks
│   │   │   ├── useAuth.ts        # Authentication hook
│   │   │   ├── useToast.ts       # Toast notification hook
│   │   │   └── api/              # API-specific hooks
│   │   │       ├── useAuth.ts    # Authentication API hook
│   │   │       └── useUsers.ts   # User management API hook
│   │   ├── stores/               # Zustand state management
│   │   │   └── authStore.ts      # Authentication state store
│   │   ├── lib/                  # Core utilities and configurations
│   │   │   ├── utils.ts          # General utility functions
│   │   │   ├── api.ts            # API client configuration
│   │   │   ├── auth.ts           # Authentication utilities
│   │   │   └── constants.ts      # Application constants
│   │   ├── types/                # Global TypeScript types
│   │   │   └── api.ts            # API type definitions
│   │   └── test/                 # Test utilities and configurations
│   │       ├── setup.ts          # Test setup configuration
│   │       ├── utils.tsx         # Test utility functions
│   │       ├── factories/        # Test data factories
│   │       │   ├── userFactories.ts # User test data factories
│   │       │   └── componentFactories.ts # Component test data factories
│   │       ├── integration/      # Integration tests
│   │       │   ├── auth-integration.test.tsx # Authentication flow tests
│   │       │   └── component-management-integration.test.tsx # Component management tests
│   │       └── e2e/              # End-to-end tests (archived)
│   │           ├── auth-flow.spec.ts # Authentication E2E tests
│   │           └── component-management.spec.ts # Component management E2E tests
│   ├── tests/                    # Playwright E2E tests
│   │   ├── auth-flow.spec.ts     # Authentication flow E2E tests
│   │   └── component-management.spec.ts # Component management E2E tests
│   ├── public/                   # Static assets
│   │   ├── favicon.ico           # Application favicon
│   │   ├── logo.svg              # Application logo
│   │   └── images/               # Image assets
│   ├── styles/                   # Global styles (if needed)
│   ├── scripts/                  # Build and development scripts
│   ├── components.json           # shadcn/ui configuration
│   ├── package.json              # Node.js dependencies and scripts
│   ├── package-lock.json         # Dependency lock file
│   ├── next.config.js            # Next.js configuration
│   ├── tailwind.config.ts        # Tailwind CSS configuration
│   ├── tsconfig.json             # TypeScript configuration
│   ├── postcss.config.js         # PostCSS configuration
│   ├── playwright.config.ts      # Playwright test configuration
│   ├── vitest.config.ts          # Vitest test configuration
│   ├── .eslintrc.json            # ESLint configuration
│   ├── .env                      # Environment variables (not in VCS)
│   └── next-env.d.ts             # Next.js type definitions
│
├── cad-integrator-service/        # C# CAD Integration Service
│   ├── src/                      # C# source code
│   │   ├── Program.cs            # Application entry point
│   │   ├── Controllers/          # API controllers
│   │   ├── Services/             # Business logic services
│   │   ├── Models/               # Data models
│   │   ├── AutoCAD/              # AutoCAD integration
│   │   └── Configuration/        # Service configuration
│   ├── tests/                    # C# test project
│   ├── appsettings.json          # Application settings
│   ├── appsettings.Development.json # Development settings
│   ├── CADIntegrator.csproj      # C# project file
│   └── Dockerfile                # Container configuration
│
├── computation-engine-service/    # C# Computation Engine Service
│   ├── src/                      # C# source code
│   │   ├── Program.cs            # Application entry point
│   │   ├── Controllers/          # API controllers
│   │   ├── Services/             # Calculation services
│   │   ├── Models/               # Calculation models
│   │   ├── Calculations/         # Electrical calculations
│   │   │   ├── HeatTracing/      # Heat tracing calculations
│   │   │   ├── CableSizing/      # Cable sizing calculations
│   │   │   └── LoadAnalysis/     # Load analysis calculations
│   │   └── Standards/            # Standards compliance
│   │       ├── IEC/              # IEC standards implementation
│   │       └── EN/               # EN standards implementation
│   ├── tests/                    # C# test project
│   ├── appsettings.json          # Application settings
│   ├── appsettings.Development.json # Development settings
│   ├── ComputationEngine.csproj  # C# project file
│   └── Dockerfile                # Container configuration
│
└── docs/                         # Comprehensive documentation
    ├── README.md                 # Documentation overview
    ├── developer-handbooks/      # Developer guidance
    │   ├── 001-cover.md         # Developer handbook cover
    │   ├── 020-getting-started.md # Getting started guide
    │   ├── 030-development-standards.md # Development standards
    │   ├── 040-unified-patterns.md # Unified patterns guide
    │   ├── 050-backend-development.md # Backend development guide
    │   ├── 060-frontend-transition.md # Frontend transition guide
    │   ├── 070-testing-framework.md # Testing framework guide
    │   ├── 080-script-ecosystem.md # Script ecosystem guide
    │   ├── 090-component-models.md # Component models guide
    │   ├── 100-database-management.md # Database management guide
    │   ├── 110-docker-dev-deployment.md # Docker deployment guide
    │   ├── frontend/             # Frontend-specific documentation
    │   │   ├── 000-frontend-specification.md # Frontend specification
    │   │   ├── frontend-developer-handbook/ # Frontend development guides
    │   │   └── frontend-typing-handbook/ # TypeScript guides
    │   └── backend/              # Backend-specific documentation
    │       └── 000-backend-specification.md # Backend specification
    ├── api/                      # API documentation
    │   └── advanced-component-management.md # Component API documentation
    ├── tasks/                    # Task documentation
    │   └── completed/            # Completed task documentation
    │       └── component-management/ # Component management task docs
    ├── testing/                  # Testing documentation
    │   └── test-fix-summary-2025-01-16.md # Test fix summary
    ├── calculations/             # Electrical calculation documentation
    │   ├── 001-cable-sizing-calculations.md # Cable sizing documentation
    │   └── 002-heat-loss-calculations.md # Heat loss documentation
    ├── ai-agent-team/           # AI agent framework documentation
    │   ├── README.md            # AI agent team overview
    │   ├── index.md             # AI agent index
    │   ├── framework-summary.md # Framework summary
    │   ├── quality-assurance.md # Quality assurance processes
    │   ├── coordination-protocols.md # Coordination protocols
    │   ├── agent-training.md    # Agent training documentation
    │   ├── performance-monitoring.md # Performance monitoring
    │   └── agent-implementation-guides.md # Implementation guides
    ├── 001-development-roadmap.md # Development roadmap
    ├── 002-robust-design-principles.md # Design principles
    ├── 003-implementation-methodology.md # Implementation methodology
    └── 004-methodology-template.md # Methodology template
```

## Module Relationships

### Backend Service Layers
1. **API Layer** (`src/api/v1/`): RESTful endpoints with OpenAPI documentation
2. **Schema Layer** (`src/core/schemas/`): Pydantic validation and serialization
3. **Service Layer** (`src/core/services/`): Business logic and domain rules
4. **Repository Layer** (`src/core/repositories/`): Data access and persistence
5. **Model Layer** (`src/core/models/`): SQLAlchemy ORM entities

### Frontend Module Structure
- **App Router** (`src/app/`): Next.js routing and page components
- **UI Components** (`src/components/ui/`): shadcn/ui design system components
- **Feature Modules** (`src/modules/`): Domain-driven design modules with complete functionality
- **Hooks** (`src/hooks/`): React hooks for state management and API integration
- **Stores** (`src/stores/`): Zustand state management for client-side state

### Integration Services
- **CAD Integration** (`cad-integrator-service/`): AutoCAD integration with .NET API
- **Computation Engine** (`computation-engine-service/`): High-performance electrical calculations

## Architectural Boundaries

### Service Boundaries
- **Backend Service**: Handles all business logic, data persistence, and API operations
- **Frontend Application**: Manages user interface, user experience, and client-side state
- **CAD Integration Service**: Provides CAD software integration and drawing generation
- **Computation Engine Service**: Delivers high-performance electrical calculations

### Domain Boundaries
- **Authentication Domain**: User authentication, authorization, and session management
- **User Management Domain**: User profiles, preferences, and administrative functions
- **Component Management Domain**: Electrical component catalog and specifications
- **Project Management Domain**: Project lifecycle and electrical system design (planned)
- **Calculation Domain**: Electrical calculations and analysis (planned)
- **Standards Compliance Domain**: IEC/EN standards validation and reporting (planned)

### Data Flow Boundaries
- **API Communication**: REST APIs with JSON payloads between services
- **Database Access**: SQLAlchemy ORM with PostgreSQL/SQLite databases
- **Client State**: Zustand stores for client-side state management
- **Server State**: React Query for server state caching and synchronization

## Development Workflow Structure

### Code Quality Gates
- **Backend**: MyPy type checking, Ruff linting, Bandit security scanning, Pytest testing
- **Frontend**: TypeScript strict mode, ESLint linting, Prettier formatting, Vitest/Playwright testing
- **Integration**: Docker Compose for multi-service development environment

### Documentation Structure
- **Developer Handbooks**: Comprehensive guides for development standards and patterns
- **API Documentation**: OpenAPI/Swagger documentation with examples
- **Task Documentation**: Implementation task breakdown and completion tracking
- **Calculation Documentation**: Electrical engineering calculation specifications

### Testing Structure
- **Unit Tests**: Individual component and function testing
- **Integration Tests**: Multi-component workflow testing
- **End-to-End Tests**: Complete user workflow testing with Playwright
- **Performance Tests**: Load testing and performance benchmarking

This structure ensures clear separation of concerns, maintainable code organization, and scalable architecture for the Ultimate Electrical Designer platform.